import {Box, Divider, Typography} from '@mui/material';
import EditIcon from 'Assets/EditIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import Form from 'Components/Forms/Form';
import {FormActions} from 'Components/Forms/Form/FormUtils';
import React from 'react';
import {useNavigate} from 'react-router-dom';
import {useGetDevicesQuery, useUpdateDevicesMutation} from 'redux/app/planManagementAPiSlice';
import {IConfigureDevice} from 'redux/app/types/plan.type';
import {RouteNames} from 'Routes/routeNames';
import DeviceLimitActions from './DeviceLimitActions';
import EditDeviceLimitsFormikForm from './EditDeviceLimits';
import {DeviceConfigFormikValues, getDeviceConfigFormValidationSchema} from './plans.utils';

const DeviceLimits: React.FC = () => {
  const navigate = useNavigate();
  const [mode, setMode] = React.useState<'view' | 'edit'>('view');
  const {data: deviceData} = useGetDevicesQuery({
    order: 'min asc',
    limit: 1000,
    offset: 0,
  });
  const [updateDevices] = useUpdateDevicesMutation();

  const PX = 1.5;
  const buildBottomSection = () => {
    return (
      <Box sx={{display: 'flex', flexDirection: 'column', mt: 'auto', mb: 1.5}} data-testid="bottom-section">
        <Divider sx={{my: 2}} />
        <Box sx={{alignSelf: 'flex-end'}} px={PX}>
          <BorderButton
            sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
            fullWidth={false}
            onClick={() => navigate(-1)}
          >
            Close
          </BorderButton>
        </Box>
      </Box>
    );
  };

  const breadcrumbs = () => {
    return [
      {
        label: 'Plans',
        url: RouteNames.PENDING_TENANTS,
      },
      {
        label: 'Device Limits',
        url: RouteNames.DEVICE_LIMITS,
      },
    ];
  };

  const onSubmit = async (_formValues: DeviceConfigFormikValues, _action: FormActions<DeviceConfigFormikValues>) => {
    console.log({_formValues});

    // Filter only edited rows for submission
    const editedItems = _formValues.items.filter(item => item.isEdited);
    console.log('Edited rows:', editedItems);

    const transformedData = editedItems.map(item => {
      const tempItem = item as unknown as IConfigureDevice;
      return {
        min: Number(item.min),
        max: Number(item.max),
        id: tempItem.id,
      };
    });

    console.log({transformedData});

    // Only submit if there are edited items
    if (transformedData.length === 0) {
      console.log('No changes detected, skipping submission');
      setMode('view');
      return;
    }

    // TODO: Submit form data
    try {
      const res = await updateDevices(transformedData as unknown as IConfigureDevice[]);
      console.log({res});
      if (res.error) {
        console.log({error: res.error});
      }
      // setMode('view');
    } catch (error) {
      console.log({error});
    }
  };

  const getInitialValue = (): DeviceConfigFormikValues => {
    // Transform API data to match form structure
    if (deviceData && deviceData.length > 0) {
      return {
        items: deviceData.map((item: any) => ({
          ...item,
          isEdited: false, // Initialize tracking field
        })),
      };
    }

    // Fallback when no data
    return {
      items: [
        {
          min: undefined,
          max: undefined,
          isEdited: false, // Initialize tracking field
        },
      ],
    };
  };

  // Recalculate initial values when deviceData changes
  const initialValues = React.useMemo(() => getInitialValue(), [deviceData]);

  const buildTopPart = () => {
    return (
      <Box display="flex" alignItems="center" gap={1} data-testid="top-part">
        <Box display={'flex'} flexDirection={'column'} gap={1}>
          <Box display={'flex'} flexDirection={'row'} alignItems="center" gap={1}>
            <Typography sx={{fontSize: '1.25rem', fontWeight: 700}}>Device Limits</Typography>
          </Box>
          <Box sx={{display: 'flex', alignItems: 'center'}}>
            <Box
              sx={{
                height: 12,
                borderColor: 'body.100',
              }}
            />
            <Breadcrumb items={breadcrumbs()} separator="|" />
          </Box>
        </Box>
        <Box sx={{ml: 'auto', display: 'flex', flexDirection: 'row', gap: 1, alignItems: 'center'}}>
          <BorderButton
            data-testid="invalid-button"
            sx={{fontWeight: 700, px: '1.56rem', color: 'body.dark'}}
            onClick={() => setMode('edit')}
          >
            <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}} data-testid="invalid-button">
              <EditIcon
                sx={{
                  fontSize: '1.25rem',
                  fill: theme => theme.palette.white.main,
                  color: 'body.500',
                }}
                data-testid="edit-icon"
              />
              Edit
            </Box>
          </BorderButton>
        </Box>
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0.5,
      }}
    >
      {buildTopPart()}

      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1.5,
        }}
      >
        <Box
          sx={{
            border: '1px solid',
            borderColor: 'body.200',
            borderRadius: '0.375rem',
            pt: 1,
            minHeight: '80vh',
            display: 'flex',
            flexDirection: 'column',
          }}
          data-testid="lead-detail"
        >
          <Form
            initialValues={initialValues}
            enableReinitialize={true}
            validateOnChange={true}
            validateOnBlur={true}
            validationSchema={getDeviceConfigFormValidationSchema()}
            onSubmit={(v, a) => onSubmit(v, a)}
          >
            <Box sx={{display: 'flex', flexDirection: 'column', flex: 1, minHeight: '80vh'}}>
              <EditDeviceLimitsFormikForm mode={mode} sx={{px: 1.5}} />
              {mode === 'edit' && (
                <>
                  <Divider sx={{my: 2, mt: 'auto'}} />
                  <DeviceLimitActions mode={mode} setMode={setMode} sx={{px: 1.5}} />
                </>
              )}
            </Box>
          </Form>

          {mode === 'view' && buildBottomSection()}
        </Box>
      </Box>
    </Box>
  );
};

export default DeviceLimits;
