import {Box, BoxProps} from '@mui/material';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import {useFormikContext} from 'formik';
import {useNavigate} from 'react-router';
import {DeviceConfigForm} from './plans.utils';

export interface DeviceLimitActionsProps extends BoxProps {
  mode: 'view' | 'edit';
  setMode: (mode: 'view' | 'edit') => void;
}

/**
 * Bottom action buttons for the Add User page
 * @param param - Props for the bottom action buttons
 * @returns JSX.Element
 */
const DeviceLimitActions: React.FC<DeviceLimitActionsProps> = ({mode, setMode, ...rest}) => {
  const {handleSubmit, isSubmitting, isValid, dirty, isValidating} = useFormikContext<{
    items: DeviceConfigForm[];
  }>();

  // Enable submit only when form is valid, has changes, not submitting, and not validating
  const canSubmit = !isSubmitting && isValid && dirty && !isValidating;

  const navigate = useNavigate();

  const getSubmitTitle = () => {
    if (isSubmitting) {
      return 'Saving...';
    }
    return 'Save Changes';
  };

  return (
    <Box {...rest} display="flex" justifyContent="flex-end" gap={2} sx={{mb: 1.5, mr: 1.5}}>
      <BorderButton
        data-testid="close-button"
        onClick={() => {
          setMode('view');
        }}
        sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
      >
        Cancel
      </BorderButton>
      <BlueButton
        data-testid="submit-button"
        loadingPosition="end"
        loading={isSubmitting}
        disabled={!canSubmit}
        sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
        onClick={() => handleSubmit()}
      >
        {getSubmitTitle()}
      </BlueButton>
    </Box>
  );
};

export default DeviceLimitActions;
