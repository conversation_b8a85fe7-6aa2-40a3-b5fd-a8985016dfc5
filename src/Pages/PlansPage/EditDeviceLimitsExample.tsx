import React from 'react';
import { useFormikContext } from 'formik';
import { DeviceConfigFormikValues, getEditedRows, getEditedRowsCount } from './plans.utils';

/**
 * Example component showing how to use the row tracking functionality
 */
const EditDeviceLimitsExample: React.FC = () => {
  const { values } = useFormikContext<DeviceConfigFormikValues>();

  // Get all edited rows with their original indices
  const editedRows = getEditedRows(values);
  
  // Get count of edited rows
  const editedCount = getEditedRowsCount(values);

  // Example: Log edited rows when form changes
  React.useEffect(() => {
    if (editedCount > 0) {
      console.log(`${editedCount} rows have been edited:`);
      editedRows.forEach((row, index) => {
        console.log(`Row ${row.originalIndex}: min=${row.min}, max=${row.max}`);
      });
    }
  }, [editedCount, editedRows]);

  return (
    <div>
      <p>Edited rows count: {editedCount}</p>
      {editedRows.length > 0 && (
        <div>
          <h4>Edited rows:</h4>
          <ul>
            {editedRows.map((row) => (
              <li key={row.originalIndex}>
                Row {row.originalIndex}: min={row.min}, max={row.max}
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default EditDeviceLimitsExample;
